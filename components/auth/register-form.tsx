"use client"

import { motion } from "framer-motion"

import Link from "next/link"
import { useCallback, useEffect, useState } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { countries } from "@/data/countries"
import { encryptPassword } from "@/utils/encryption"
import { checkUsername } from "@/utils/users-api"
import { api } from "@/utils/api"
import TurnstileWidget from "./cloudflare-turnstile"

const membershipTypes = [
  { id: 1, value: 1, label: "Student" },
  { id: 2, value: 2, label: "Non-Student Member" },
]

const genders = [
  { value: "male", label: "Male" },
  { value: "female", label: "Female" },
]

// 使用从data/countries.ts导入的countries数组

export default function RegisterForm() {
  const [form, setForm] = useState({
    username: "",
    password: "",
    confirmPassword: "",
    name: "",
    email: "",
    gender: "male",
    membership: "1",
    country: "",
    organization: "",
    token: "",
  })

  const [showCountryDropdown, setShowCountryDropdown] = useState(false)
  const [turnstileToken, setTurnstileToken] = useState<string>("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [turnstileResetTrigger, setTurnstileResetTrigger] = useState(0)
  const [isCheckingUsername, setIsCheckingUsername] = useState(false)
  const [lastCheckedUsername, setLastCheckedUsername] = useState("")
  const [usernameValidationStatus, setUsernameValidationStatus] = useState<"none" | "checking" | "valid" | "invalid">(
    "none"
  )

  const [formErrors, setFormErrors] = useState({
    username: "",
    password: "",
    confirmPassword: "",
    email: "",
    country: "",
    organization: "",
  })
  const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:3001"

  const validateEmail = (email: string) => {
    const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/
    return emailRegex.test(email)
  }

  const validateField = (name: string, value: string) => {
    let error = ""

    // 检查空格 - 除了 name 和 organization 字段外，其他字段不允许包含空格
    if (name !== "name" && name !== "organization" && value.includes(" ")) {
      error = "This field cannot contain spaces"
      return error
    }

    switch (name) {
      case "username":
        if (value.length < 3) {
          error = "Username must be at least 3 characters"
        }
        break
      case "password":
        if (value.length < 8) {
          error = "Password must be at least 8 characters"
        }
        break
      case "confirmPassword":
        if (value !== form.password) {
          error = "Passwords do not match"
        }
        break
      case "email":
        if (value.length > 64) {
          error = "Email address must be at least 64"
        }
        if (!validateEmail(value)) {
          error = "Please enter a valid email address"
        }
        break
      case "country":
        if (!countries.includes(value)) {
          error = "Please select a country from the list"
        }
        break
    }

    return error
  }

  // 检查用户名是否存在
  const handleUsernameCheck = async (username: string) => {
    if (!username || username.length < 3) {
      setUsernameValidationStatus("none")
      setLastCheckedUsername("")
      return // 用户名太短，不进行检查
    }

    // 如果用户名没有改变，不重复检查
    if (username === lastCheckedUsername) {
      return
    }

    setIsCheckingUsername(true)
    setUsernameValidationStatus("checking")
    try {
      const result = await checkUsername(username)
      setLastCheckedUsername(username)

      if (result.exist) {
        setFormErrors((prev) => ({ ...prev, username: "Username already exists" }))
        setUsernameValidationStatus("invalid")
      } else {
        // 如果用户名不存在，清除之前的错误（如果有的话）
        setFormErrors((prev) => ({ ...prev, username: "" }))
        setUsernameValidationStatus("valid")
      }
    } catch (error) {
      console.error("Username check error:", error)
      // 检查失败时不显示错误，允许用户继续，但重置状态
      setUsernameValidationStatus("none")
      setLastCheckedUsername("")
    } finally {
      setIsCheckingUsername(false)
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setForm({ ...form, [name]: value })

    // 如果是用户名字段改变，重置验证状态
    if (name === "username") {
      if (value !== lastCheckedUsername) {
        setUsernameValidationStatus("none")
      }
    }

    // Validate field if it's one we care about - 包含所有需要验证空格的字段
    if (
      [
        "username",
        "password",
        "confirmPassword",
        "email",
        "country",
        "name",
        "phone",
        "organization",
        "position",
      ].includes(name)
    ) {
      const error = validateField(name, value)
      setFormErrors((prev) => ({ ...prev, [name]: error }))
    }

    // Special case for confirmPassword - validate when password changes
    if (name === "password") {
      const confirmError = form.confirmPassword ? (value !== form.confirmPassword ? "Passwords do not match" : "") : ""
      setFormErrors((prev) => ({ ...prev, confirmPassword: confirmError }))
    }
  }

  const handleCountryChange = (country: string) => {
    setForm({ ...form, country })
    setShowCountryDropdown(false)

    // Validate country
    const error = validateField("country", country)
    setFormErrors((prev) => ({ ...prev, country: error }))
  }

  useEffect(() => {
    // 添加点击事件监听器，用于关闭国家下拉框
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if (!target.closest("#register-country-dropdown-container")) {
        setShowCountryDropdown(false)
      }
    }

    if (showCountryDropdown) {
      document.addEventListener("mousedown", handleClickOutside)
    }

    // 清理函数
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [showCountryDropdown])

  const validateForm = () => {
    const newErrors = {
      username: validateField("username", form.username),
      password: validateField("password", form.password),
      confirmPassword: validateField("confirmPassword", form.confirmPassword),
      email: validateField("email", form.email),
      country: validateField("country", form.country),
      organization: validateField("organization", form.organization),
    }

    setFormErrors(newErrors)

    // Check if there are any errors
    return !Object.values(newErrors).some((error) => error !== "")
  }

  // 使用 useCallback 来稳定回调函数，避免不必要的重新渲染
  const handleTurnstileVerify = useCallback((token: string) => {
    setTurnstileToken(token)
  }, [])

  const handleTurnstileReset = useCallback(() => {
    setTurnstileToken("")
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Form validation
    if (!validateForm()) {
      setError("Please fix the errors in the form")
      return
    }

    setLoading(true)
    setError("")

    try {
      // 对密码进行加密
      const hashedPassword = await encryptPassword(form.password)

      // Make actual API call to register user
      const data = await api.post<{
        code?: number
        data?: unknown
        msg?: string
        message?: string
      }>("/api/auth/register", {
        username: form.username,
        // 发送加密后的密码
        password: hashedPassword,
        name: form.name,
        email: form.email,
        gender: form.gender,
        role: 1, // 默认为普通用户
        membership: parseInt(form.membership),
        country: form.country,
        organization: form.organization,
        "cf-turnstile-response": turnstileToken,
      })

      // 检查响应是否成功 - 支持新旧格式
      if (data.code && data.code !== 200) {
        throw new Error(data.msg || data.message || "Registration failed")
      }

      // Registration successful - redirect to login page
      window.location.href = "/login"
    } catch (error) {
      setError(error instanceof Error ? error.message : "Registration failed. Please try again later")

      // 重置Turnstile token，因为它已经被使用过了
      setTurnstileToken("")
      setTurnstileResetTrigger((prev) => prev + 1)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 px-4 py-12">
      <div className="flex min-h-[calc(100vh-6rem)] items-center justify-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mx-auto w-full max-w-4xl"
        >
          <Card className="border border-gray-200 bg-white shadow-lg">
            <CardHeader className="pt-8 pb-6 text-center">
              <CardTitle className="mb-2 text-2xl font-bold text-gray-900">Create Account</CardTitle>
              <CardDescription className="text-gray-600">
                Register for the International Forum on Maize Biology 2025
              </CardDescription>
            </CardHeader>

            <CardContent className="px-8 pt-6 pb-8">
              <form onSubmit={handleSubmit} className="space-y-8">
                {/* Basic Information */}
                <div className="space-y-4">
                  <h3 className="border-b border-gray-200 pb-2 text-lg font-medium text-gray-900">Basic Information</h3>

                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div>
                      <label htmlFor="username" className="mb-1 block text-sm font-medium text-gray-700">
                        Username <span className="text-red-500">*</span>
                      </label>
                      <div className="relative">
                        <Input
                          id="username"
                          name="username"
                          placeholder="Enter username (min. 3 characters)"
                          value={form.username}
                          onChange={handleChange}
                          onBlur={(e) => handleUsernameCheck(e.target.value)}
                          required
                          className={`w-full ${
                            formErrors.username
                              ? "border-red-500"
                              : usernameValidationStatus === "valid"
                                ? "border-green-500"
                                : ""
                          }`}
                        />
                        {isCheckingUsername && (
                          <div className="absolute top-1/2 right-3 -translate-y-1/2">
                            <i className="fas fa-spinner fa-spin text-gray-400"></i>
                          </div>
                        )}
                        {usernameValidationStatus === "valid" && !isCheckingUsername && (
                          <div className="absolute top-1/2 right-3 -translate-y-1/2">
                            <i className="fas fa-check text-green-500"></i>
                          </div>
                        )}
                      </div>
                      {formErrors.username && <p className="mt-1 text-xs text-red-500">{formErrors.username}</p>}
                      {usernameValidationStatus === "valid" && !formErrors.username && (
                        <p className="mt-1 text-xs text-green-600">Username is available</p>
                      )}
                    </div>

                    <div>
                      <label htmlFor="name" className="mb-1 block text-sm font-medium text-gray-700">
                        Full Name <span className="text-red-500">*</span>
                      </label>
                      <Input
                        id="name"
                        name="name"
                        placeholder="Enter your full name"
                        value={form.name}
                        onChange={handleChange}
                        required
                        className="w-full"
                      />
                    </div>

                    <div>
                      <label htmlFor="password" className="mb-1 block text-sm font-medium text-gray-700">
                        Password <span className="text-red-500">*</span>
                      </label>
                      <Input
                        id="password"
                        name="password"
                        type="password"
                        placeholder="Enter password (min. 8 characters)"
                        value={form.password}
                        onChange={handleChange}
                        required
                        className={`w-full ${formErrors.password ? "border-red-500" : ""}`}
                      />
                      {formErrors.password && <p className="mt-1 text-xs text-red-500">{formErrors.password}</p>}
                    </div>

                    <div>
                      <label htmlFor="confirmPassword" className="mb-1 block text-sm font-medium text-gray-700">
                        Confirm Password <span className="text-red-500">*</span>
                      </label>
                      <Input
                        id="confirmPassword"
                        name="confirmPassword"
                        type="password"
                        placeholder="Confirm password"
                        value={form.confirmPassword}
                        onChange={handleChange}
                        required
                        className={`w-full ${formErrors.confirmPassword ? "border-red-500" : ""}`}
                      />
                      {formErrors.confirmPassword && (
                        <p className="mt-1 text-xs text-red-500">{formErrors.confirmPassword}</p>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                    <div>
                      <label htmlFor="gender" className="mb-1 block text-sm font-medium text-gray-700">
                        Gender <span className="text-red-500">*</span>
                      </label>
                      <select
                        id="gender"
                        name="gender"
                        value={form.gender}
                        onChange={handleChange}
                        className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-green-500 focus:ring-green-500 focus:outline-none"
                        required
                      >
                        {genders.map((item) => (
                          <option key={item.value} value={item.value}>
                            {item.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label htmlFor="membership" className="mb-1 block text-sm font-medium text-gray-700">
                        Membership Type <span className="text-red-500">*</span>
                      </label>
                      <select
                        id="membership"
                        name="membership"
                        value={form.membership}
                        onChange={handleChange}
                        className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:border-green-500 focus:ring-green-500 focus:outline-none"
                        required
                      >
                        {membershipTypes.map((item) => (
                          <option key={item.value} value={item.id}>
                            {item.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label htmlFor="country" className="mb-1 block text-sm font-medium text-gray-700">
                        Country <span className="text-red-500">*</span>
                      </label>
                      <div className="relative" id="register-country-dropdown-container">
                        <Input
                          id="country"
                          name="country"
                          value={form.country}
                          onChange={(e) => {
                            handleChange(e)
                            setShowCountryDropdown(true)
                          }}
                          className={`w-full ${formErrors.country ? "border-red-500" : ""}`}
                          placeholder="Type to search countries"
                          onFocus={() => setShowCountryDropdown(true)}
                          required
                        />
                        {formErrors.country && <p className="mt-1 text-xs text-red-500">{formErrors.country}</p>}
                        {showCountryDropdown && (
                          <div className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md border bg-white shadow-lg">
                            {countries
                              .filter((country) => country.toLowerCase().includes((form.country || "").toLowerCase()))
                              .slice(0, 5)
                              .map((country, index) => (
                                <div
                                  key={index}
                                  className="cursor-pointer px-4 py-2 hover:bg-gray-100"
                                  onClick={() => handleCountryChange(country)}
                                >
                                  {country}
                                </div>
                              ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Contact Information */}
                <div className="space-y-4">
                  <h3 className="border-b border-gray-200 pb-2 text-lg font-medium text-gray-900">
                    Contact Information
                  </h3>

                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                    <div>
                      <label htmlFor="email" className="mb-1 block text-sm font-medium text-gray-700">
                        Email Address <span className="text-red-500">*</span>
                      </label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={form.email}
                        onChange={handleChange}
                        required
                        className={`w-full ${formErrors.email ? "border-red-500" : ""}`}
                      />
                      {formErrors.email && <p className="mt-1 text-xs text-red-500">{formErrors.email}</p>}
                    </div>

                    <div>
                      <label htmlFor="organization" className="mb-1 block text-sm font-medium text-gray-700">
                        Organization <span className="text-red-500">*</span>
                      </label>
                      <Input
                        id="organization"
                        name="organization"
                        placeholder="Enter your organization"
                        value={form.organization}
                        onChange={handleChange}
                        required
                        className={`w-full ${formErrors.organization ? "border-red-500" : ""}`}
                      />
                      {formErrors.organization && (
                        <p className="mt-1 text-xs text-red-500">{formErrors.organization}</p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Verification */}
                <div className="space-y-4">
                  <h3 className="border-b border-gray-200 pb-2 text-lg font-medium text-gray-900">Verification</h3>
                  <div className="flex justify-center">
                    <TurnstileWidget
                      onVerify={handleTurnstileVerify}
                      onReset={handleTurnstileReset}
                      resetTrigger={turnstileResetTrigger}
                      theme="light"
                      language="en"
                    />
                  </div>
                </div>

                {/* Error message */}
                {error && (
                  <div className="rounded-md border border-red-200 bg-red-50 p-3 text-center">
                    <span className="text-sm text-red-600">{error}</span>
                  </div>
                )}

                {/* Submit button */}
                <div className="pt-4">
                  <Button
                    type="submit"
                    className="w-full bg-green-600 py-2 font-medium text-white hover:bg-green-700"
                    disabled={loading}
                  >
                    {loading ? "Creating Account..." : "Create Account"}
                  </Button>

                  <p className="mt-4 text-center text-sm text-gray-600">
                    Already have an account?{" "}
                    <Link href="/login" className="font-medium text-green-600 hover:text-green-500">
                      Sign in
                    </Link>
                  </p>
                </div>
              </form>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
