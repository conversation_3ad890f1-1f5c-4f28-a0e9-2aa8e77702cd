"use client"

import { motion } from "framer-motion"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useCallback, useEffect, useState } from "react"
import TurnstileWidget from "@/components/auth/cloudflare-turnstile"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { encryptPassword } from "@/utils/encryption"
import { api } from "@/utils/api"

export default function LoginForm() {
  const router = useRouter()
  const [form, setForm] = useState({
    username: "",
    password: "",
  })
  const [turnstileToken, setTurnstileToken] = useState<string>("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [turnstileResetTrigger, setTurnstileResetTrigger] = useState(0)
  const [fieldErrors, setFieldErrors] = useState({ username: "", password: "" })
  const [isCheckingAuth, setIsCheckingAuth] = useState(true)

  // 检查用户是否已经登录
  useEffect(() => {
    const checkAuthStatus = () => {
      try {
        const userData = localStorage.getItem("user")
        if (userData) {
          const user = JSON.parse(userData) as { token?: string }
          // 检查token是否存在且有效
          if (user.token) {
            // 用户已登录，重定向到dashboard
            router.push("/dashboard")
            return
          }
        }
      } catch (error) {
        console.error("Error checking auth status:", error)
        // 清除无效的用户数据
        localStorage.removeItem("user")
      }
      setIsCheckingAuth(false)
    }

    checkAuthStatus()
  }, [router])

  // 检查是否有来自认证过期的错误消息
  useEffect(() => {
    const authErrorMessage = sessionStorage.getItem("auth_error_message")
    if (authErrorMessage) {
      setError(authErrorMessage)
      sessionStorage.removeItem("auth_error_message")
    }
  }, [])

  const validateField = (name: string, value: string) => {
    let error = ""

    // 检查空格 - 用户名和密码都不允许包含空格
    if (value.includes(" ")) {
      error = "This field cannot contain spaces"
      return error
    }

    switch (name) {
      case "username":
        if (value.length < 3) {
          error = "Username must be at least 3 characters"
        }
        break
      case "password":
        if (value.length < 8) {
          error = "Password must be at least 8 characters"
        }
        break
    }

    return error
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setForm({ ...form, [name]: value })

    // 实时验证
    const error = validateField(name, value)
    setFieldErrors((prev) => ({ ...prev, [name]: error }))
  }

  // 使用 useCallback 来稳定回调函数，避免不必要的重新渲染
  const handleTurnstileVerify = useCallback((token: string) => {
    setTurnstileToken(token)
  }, [])

  const handleTurnstileReset = useCallback(() => {
    setTurnstileToken("")
  }, [])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError("")

    // 验证表单
    if (!form.username || !form.password) {
      setError("Please enter both username and password")
      setLoading(false)
      return
    }

    // 验证用户名和密码不能包含空格
    if (form.username.includes(" ")) {
      setError("Username cannot contain spaces")
      setLoading(false)
      return
    }

    if (form.password.includes(" ")) {
      setError("Password cannot contain spaces")
      setLoading(false)
      return
    }

    // 验证Turnstile
    if (!turnstileToken) {
      setError("Please complete the security verification")
      setLoading(false)
      return
    }

    try {
      // 对密码进行加密，使用与注册相同的加密方式
      const hashedPassword = await encryptPassword(form.password)

      // 调用登录API
      // 注意：我们发送前端加密后的密码，确保与注册时发送的密码格式一致
      // 后端会对这个已经加密的密码进行二次加密并与数据库中存储的值进行比对
      const responseData = await api.post<{
        code?: number
        data?: {
          token: string
          user_info: Record<string, unknown>
        }
        msg?: string
        message?: string
      }>("/api/auth/login", {
        username: form.username,
        password: hashedPassword, // 发送前端加密后的密码
        "cf-turnstile-response": turnstileToken,
      })

      // 检查响应是否成功 - 支持新旧格式
      if (responseData.code && responseData.code !== 200) {
        throw new Error(responseData.msg || responseData.message || "Login failed")
      }

      // 确保有数据返回
      if (!responseData.data) {
        throw new Error("Invalid response format from server")
      }

      // 登录成功，获取用户数据
      const userData = responseData

      // 保存用户信息到localStorage
      localStorage.setItem("user", JSON.stringify(userData.data))
      // 重定向到首页或仪表盘
      window.location.href = "/"
    } catch (error) {
      setError(error instanceof Error ? error.message : "Invalid username or password")

      // 重置Turnstile token，因为它已经被使用过了
      setTurnstileToken("")
      setTurnstileResetTrigger((prev) => prev + 1)
    } finally {
      setLoading(false)
    }
  }

  // 如果正在检查认证状态，显示加载状态
  if (isCheckingAuth) {
    return (
      <div className="min-h-screen bg-gray-50 px-4 py-12">
        <div className="flex min-h-[calc(100vh-6rem)] items-center justify-center">
          <div className="text-center">
            <div className="mb-4">
              <i className="fas fa-spinner fa-spin text-4xl text-green-600"></i>
            </div>
            <p className="text-gray-600">Checking login status...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 px-4 py-12">
      <div className="flex min-h-[calc(100vh-6rem)] items-center justify-center">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mx-auto w-full max-w-lg"
        >
          <Card className="border border-gray-200 bg-white shadow-lg">
            <CardHeader className="pt-8 pb-6 text-center">
              <CardTitle className="mb-2 text-2xl font-bold text-gray-900">Welcome Back</CardTitle>
              <CardDescription className="text-gray-600">
                Sign in to your International Forum on Maize Biology account
              </CardDescription>
            </CardHeader>

            <CardContent className="px-8 pt-6 pb-8">
              <form onSubmit={handleSubmit} className="space-y-8">
                {/* Login Fields */}
                <div className="space-y-4">
                  <h3 className="border-b border-gray-200 pb-2 text-lg font-medium text-gray-900">
                    Account Information
                  </h3>

                  <div>
                    <label htmlFor="username" className="mb-1 block text-sm font-medium text-gray-700">
                      Username <span className="text-red-500">*</span>
                    </label>
                    <Input
                      id="username"
                      name="username"
                      placeholder="Enter your username"
                      value={form.username}
                      onChange={handleChange}
                      required
                      className={`w-full ${fieldErrors.username ? "border-red-500" : ""}`}
                    />
                    {fieldErrors.username && <p className="mt-1 text-xs text-red-500">{fieldErrors.username}</p>}
                  </div>

                  <div>
                    <label htmlFor="password" className="mb-1 block text-sm font-medium text-gray-700">
                      Password <span className="text-red-500">*</span>
                    </label>
                    <Input
                      id="password"
                      name="password"
                      type="password"
                      placeholder="Enter your password"
                      value={form.password}
                      onChange={handleChange}
                      required
                      className={`w-full ${fieldErrors.password ? "border-red-500" : ""}`}
                    />
                    {fieldErrors.password && <p className="mt-1 text-xs text-red-500">{fieldErrors.password}</p>}
                  </div>
                </div>

                {/* Verification */}
                <div className="space-y-4">
                  <h3 className="border-b border-gray-200 pb-2 text-lg font-medium text-gray-900">Verification</h3>
                  <div className="flex justify-center">
                    <TurnstileWidget
                      onVerify={handleTurnstileVerify}
                      onReset={handleTurnstileReset}
                      resetTrigger={turnstileResetTrigger}
                      theme="light"
                      language="en"
                    />
                  </div>
                </div>

                {/* Error message */}
                {error && (
                  <div className="rounded-md border border-red-200 bg-red-50 p-3 text-center">
                    <span className="text-sm text-red-600">{error}</span>
                  </div>
                )}

                {/* Submit button */}
                <div className="pt-4">
                  <Button
                    type="submit"
                    className="w-full bg-green-600 py-2 font-medium text-white hover:bg-green-700"
                    disabled={loading}
                  >
                    {loading ? "Signing in..." : "Sign In"}
                  </Button>

                  <div className="mt-4 flex justify-between text-sm">
                    <Link href="/forgot-password" className="font-medium text-green-600 hover:text-green-500">
                      Forgot password?
                    </Link>
                    <Link href="/register" className="font-medium text-green-600 hover:text-green-500">
                      Create account
                    </Link>
                  </div>
                </div>
              </form>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
